// Initialize Material Design Components
mdc.autoInit();

// Initialize page preloader spinner
let pagePreloaderSpinner;
if (window.mdc && window.mdc.circularProgress) {
    const preloaderElement = document.getElementById('page-preloader-spinner');
    if (preloaderElement) {
        pagePreloaderSpinner = new mdc.circularProgress.MDCCircularProgress(preloaderElement);
    }
}

// Loading and Animation Functions
function showPagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.remove('hidden');

    // Start the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.open();
    }
}

function hidePagePreloader() {
    const preloader = document.getElementById('page-preloader');
    preloader.classList.add('hidden');

    // Stop the spinner animation
    if (pagePreloaderSpinner) {
        pagePreloaderSpinner.close();
    }

    // Remove preloader from DOM after animation completes
    setTimeout(() => {
        preloader.style.display = 'none';
    }, 500);
}

function showLoadingOverlay(text = 'Processing...') {
    const overlay = document.getElementById('loading-overlay');
    const loadingText = document.getElementById('loading-text');
    loadingText.textContent = text;
    overlay.classList.add('active');
}

function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    overlay.classList.remove('active');
}

// Targeted Loading Functions
function showTargetedLoading(targetSelector, text = 'Loading...') {
    const targetElement = document.querySelector(targetSelector);
    if (!targetElement) return;

    // Find or create loading overlay for this target
    let overlay = targetElement.querySelector('.target-loading-overlay');
    if (!overlay) {
        // Create overlay if it doesn't exist
        overlay = document.createElement('div');
        overlay.className = 'target-loading-overlay';
        overlay.innerHTML = `
            <div class="target-loading-content">
                <div class="simple-spinner">
                    <svg class="spinner-svg" viewBox="0 0 50 50">
                        <circle class="spinner-circle" cx="25" cy="25" r="20" fill="none" stroke="#6200ea" stroke-width="3" stroke-linecap="round" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                            <animate attributeName="stroke-array" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                            <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                        </circle>
                    </svg>
                </div>
                <div class="target-loading-text"></div>
            </div>
        `;
        targetElement.appendChild(overlay);
    }

    // Update text and show overlay
    const loadingText = overlay.querySelector('.target-loading-text');
    if (loadingText) {
        loadingText.textContent = text;
    }

    // Ensure target element has relative positioning
    const computedStyle = window.getComputedStyle(targetElement);
    if (computedStyle.position === 'static') {
        targetElement.style.position = 'relative';
    }

    overlay.classList.add('active');
}

function hideTargetedLoading(targetSelector) {
    const targetElement = document.querySelector(targetSelector);
    if (!targetElement) return;

    const overlay = targetElement.querySelector('.target-loading-overlay');
    if (overlay) {
        overlay.classList.remove('active');
    }
}

// Simulate loading delay for better UX (can be removed in production)
function simulateLoading(callback, delay = 300) {
    setTimeout(callback, delay);
}

// Checkbox functionality
function updateSelectAllState() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const rowCheckboxes = document.querySelectorAll('.row-checkbox');
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

    if (checkedBoxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedBoxes.length === rowCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    }
}

function updateSelectedRowsDisplay() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    console.log(`Selected ${checkedBoxes.length} rows`);
    // Here you can add UI updates for selected rows count
}

function getSelectedStudentIds() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');
    return Array.from(checkedBoxes).map(checkbox => {
        return checkbox.closest('.mdc-data-table__row').dataset.studentId;
    });
}

// Student Data
const studentsData = [
    {
        id: 'ST2024001',
        name: 'Alexander Thompson',
        level: 'Grade 10',
        birth: 'March 15, 2008',
        birthYear: 2008,
        gender: 'Male',
        photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 15',
        dateAdded: '2024-01-15'
    },
    {
        id: 'ST2024002',
        name: 'Sophia Rodriguez',
        level: 'Grade 11',
        birth: 'July 22, 2007',
        birthYear: 2007,
        gender: 'Female',
        photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 14',
        dateAdded: '2024-01-14'
    },
    {
        id: 'ST2024003',
        name: 'Marcus Johnson',
        level: 'Grade 9',
        birth: 'November 8, 2009',
        birthYear: 2009,
        gender: 'Male',
        photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 14',
        dateAdded: '2024-01-14'
    },
    {
        id: 'ST2024004',
        name: 'Emma Williams',
        level: 'Grade 12',
        birth: 'February 3, 2006',
        birthYear: 2006,
        gender: 'Female',
        photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 13',
        dateAdded: '2024-01-13'
    },
    {
        id: 'ST2024005',
        name: 'Daniel Chen',
        level: 'Grade 10',
        birth: 'September 12, 2008',
        birthYear: 2008,
        gender: 'Male',
        photo: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 13',
        dateAdded: '2024-01-13'
    },
    {
        id: 'ST2024006',
        name: 'Isabella Garcia',
        level: 'Grade 11',
        birth: 'May 18, 2007',
        birthYear: 2007,
        gender: 'Female',
        photo: 'https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 12',
        dateAdded: '2024-01-12'
    },
    {
        id: 'ST2024007',
        name: 'Ryan Martinez',
        level: 'Grade 9',
        birth: 'December 1, 2009',
        birthYear: 2009,
        gender: 'Male',
        photo: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 12',
        dateAdded: '2024-01-12'
    },
    {
        id: 'ST2024008',
        name: 'Olivia Davis',
        level: 'Grade 12',
        birth: 'April 7, 2006',
        birthYear: 2006,
        gender: 'Female',
        photo: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=60&h=60&fit=crop&crop=face',
        date: 'Jan 11',
        dateAdded: '2024-01-11'
    }
];

// Data Table State
let currentPage = 1;
let perPage = 10;
let sortColumn = '';
let sortDirection = '';
let searchQuery = '';
let filteredData = [...studentsData];

// Filter State
let gradeFilter = '';
let genderFilter = '';
let birthYearFilter = '';
let dateFromFilter = '';
let dateToFilter = '';

// MDC Component instances
let searchTextField;
let perPageSelect;
let dataTable;
let gradeFilterSelect;

// Modal component instances
let modalOverlay;
let modal;
let modalForm;
let modalTitle;
let modalSubmitBtn;
let modalCancelBtn;

// Filter offcanvas components
let filterGradeSelect;
let filterGenderSelect;
let filterBirthYearSelect;
let dateFromPicker;
let dateToPicker;

// Data Table Functions
function filterData() {
    // Show targeted loading for table
    showTargetedLoading('.data-table-container', 'Filtering...');

    simulateLoading(() => {
        filteredData = studentsData.filter(student => {
            // Search filter
            const matchesSearch = !searchQuery ||
                student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                student.level.toLowerCase().includes(searchQuery.toLowerCase()) ||
                student.id.toLowerCase().includes(searchQuery.toLowerCase());

            // Grade filter
            const matchesGrade = !gradeFilter || student.level === gradeFilter;

            // Gender filter
            const matchesGender = !genderFilter || student.gender === genderFilter;

            // Birth year filter
            const matchesBirthYear = !birthYearFilter || student.birthYear.toString() === birthYearFilter;

            // Date range filter
            let matchesDateRange = true;
            if (dateFromFilter || dateToFilter) {
                const studentDate = new Date(student.dateAdded);
                if (dateFromFilter) {
                    const fromDate = new Date(dateFromFilter);
                    matchesDateRange = matchesDateRange && studentDate >= fromDate;
                }
                if (dateToFilter) {
                    const toDate = new Date(dateToFilter);
                    matchesDateRange = matchesDateRange && studentDate <= toDate;
                }
            }

            return matchesSearch && matchesGrade && matchesGender && matchesBirthYear && matchesDateRange;
        });

        currentPage = 1;
        renderTable();
        updateHeaderFilterState();
        updateFilterChips();

        // Hide targeted loading
        hideTargetedLoading('.data-table-container');
    }, 300);
}

function sortData(column) {
    // Show targeted loading for table
    showTargetedLoading('.data-table-container', 'Sorting...');

    simulateLoading(() => {
        if (sortColumn === column) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortColumn = column;
            sortDirection = 'asc';
        }

        filteredData.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            if (column === 'birth') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            } else if (column === 'level') {
                // Sort grades numerically (Grade 9, Grade 10, etc.)
                aVal = parseInt(aVal.replace('Grade ', ''));
                bVal = parseInt(bVal.replace('Grade ', ''));
            }

            if (aVal < bVal) return sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        updateSortHeaders();
        renderTable();

        // Hide targeted loading
        hideTargetedLoading('.data-table-container');
    }, 250);
}

function updateSortHeaders() {
    document.querySelectorAll('.mdc-data-table__header-cell--sortable').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
        const icon = th.querySelector('.sort-icon');
        if (icon) {
            icon.textContent = 'unfold_more';
        }

        if (th.dataset.column === sortColumn) {
            th.classList.add(sortDirection === 'asc' ? 'sort-asc' : 'sort-desc');
            if (icon) {
                icon.textContent = sortDirection === 'asc' ? 'keyboard_arrow_up' : 'keyboard_arrow_down';
            }
        }
    });
}

function renderTable() {
    const tableBody = document.getElementById('table-body');
    const startIndex = (currentPage - 1) * perPage;
    const endIndex = startIndex + perPage;
    const pageData = filteredData.slice(startIndex, endIndex);

    // Clear existing content with fade out effect
    const existingRows = tableBody.querySelectorAll('.mdc-data-table__row');
    existingRows.forEach((row, index) => {
        row.style.animation = `fadeOut 0.2s ease forwards`;
        row.style.animationDelay = `${index * 0.02}s`;
    });

    setTimeout(() => {
        if (pageData.length === 0) {
            tableBody.innerHTML = `
                <tr class="mdc-data-table__row">
                    <td class="mdc-data-table__cell" colspan="8" style="text-align: center; padding: 48px;">
                        <div style="color: #757575;">
                            <span class="material-icons" style="font-size: 48px; margin-bottom: 16px; display: block;">search_off</span>
                            No students found
                        </div>
                    </td>
                </tr>
            `;
        } else {
            tableBody.innerHTML = pageData.map(student => `
                <tr class="mdc-data-table__row table-row" data-student-id="${student.id}">
                    <td class="mdc-data-table__cell mdc-data-table__cell--checkbox">
                        <div class="mdc-checkbox mdc-data-table__row-checkbox">
                            <input type="checkbox" class="mdc-checkbox__native-control row-checkbox" aria-label="Select row">
                            <div class="mdc-checkbox__background">
                                <svg class="mdc-checkbox__checkmark" viewBox="0 0 24 24">
                                    <path class="mdc-checkbox__checkmark-path" fill="none" d="M1.73,12.91 8.1,19.28 22.79,4.59"/>
                                </svg>
                                <div class="mdc-checkbox__mixedmark"></div>
                            </div>
                            <div class="mdc-checkbox__ripple"></div>
                        </div>
                    </td>
                    <td class="mdc-data-table__cell">
                        <div class="student-photo" style="background-image: url('${student.photo}')"></div>
                    </td>
                    <th class="mdc-data-table__cell" scope="row">${student.name}</th>
                    <td class="mdc-data-table__cell">${student.level}</td>
                    <td class="mdc-data-table__cell">${student.birth}</td>
                    <td class="mdc-data-table__cell">${student.id}</td>
                    <td class="mdc-data-table__cell">${student.date}</td>
                    <td class="mdc-data-table__cell">
                        <div class="table-actions">
                            <button class="mdc-icon-button" title="View Details">
                                <span class="material-icons">visibility</span>
                            </button>
                            <button class="mdc-icon-button" title="Edit">
                                <span class="material-icons">edit</span>
                            </button>
                            <button class="mdc-icon-button" title="More Options">
                                <span class="material-icons">more_vert</span>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');
        }

        updatePagination();
        attachTableEventListeners();
    }, existingRows.length > 0 ? 100 : 0);
}

function updatePagination() {
    const totalPages = Math.ceil(filteredData.length / perPage);
    const startItem = filteredData.length === 0 ? 0 : (currentPage - 1) * perPage + 1;
    const endItem = Math.min(currentPage * perPage, filteredData.length);

    // Update pagination info
    document.getElementById('pagination-info').textContent =
        `Showing ${startItem}-${endItem} of ${filteredData.length} students`;

    // Update pagination buttons
    document.getElementById('prev-page').disabled = currentPage === 1;
    document.getElementById('next-page').disabled = currentPage === totalPages || totalPages === 0;

    // Update page numbers
    const pagesContainer = document.getElementById('pagination-pages');
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    pagesContainer.innerHTML = '';
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `pagination-page ${i === currentPage ? 'active' : ''}`;
        pageBtn.textContent = i;
        pageBtn.addEventListener('click', (e) => {
            if (i !== currentPage) {
                createRipple(e, pageBtn, true);
                showTargetedLoading('.data-table-container', 'Loading...');

                simulateLoading(() => {
                    currentPage = i;
                    renderTable();
                    hideTargetedLoading('.data-table-container');
                }, 200);
            }
        });
        pagesContainer.appendChild(pageBtn);
    }
}

function attachTableEventListeners() {
    // Initialize MDC checkboxes
    document.querySelectorAll('.mdc-checkbox').forEach(checkbox => {
        if (window.mdc && window.mdc.checkbox) {
            new mdc.checkbox.MDCCheckbox(checkbox);
        }
    });

    // Add ripple effects to table rows
    document.querySelectorAll('.mdc-data-table__row.table-row').forEach(row => {
        row.addEventListener('click', (e) => {
            // Don't trigger if action button or checkbox was clicked
            if (e.target.closest('.mdc-icon-button') || e.target.closest('.mdc-checkbox')) return;

            createRipple(e, row, true);

            // Extract student data for bottom sheet
            const studentId = row.dataset.studentId;
            const student = studentsData.find(s => s.id === studentId);

            if (student) {
                const studentData = {
                    id: student.id,
                    name: student.name,
                    level: student.level,
                    details: `Born: ${student.birth} • Student ID: ${student.id}`,
                    photo: `url('${student.photo}')`
                };
                showBottomSheet(studentData);
            }
        });
    });

    // Handle individual row checkbox changes
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', (e) => {
            updateSelectAllState();
            updateSelectedRowsDisplay();
        });
    });

    // Add ripple effects to action buttons
    document.querySelectorAll('.table-actions .mdc-icon-button').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.stopPropagation();
            createRipple(e, btn, true);

            const row = btn.closest('.mdc-data-table__row');
            const studentId = row.dataset.studentId;
            const student = studentsData.find(s => s.id === studentId);
            const action = btn.title.toLowerCase();

            if (action.includes('more')) {
                // Show bottom sheet for more options
                const studentData = {
                    id: student.id,
                    name: student.name,
                    level: student.level,
                    details: `Born: ${student.birth} • Student ID: ${student.id}`,
                    photo: `url('${student.photo}')`
                };
                showBottomSheet(studentData);
            } else {
                console.log(`${action} student:`, student.name);
            }
        });
    });
}

// Header Filter Functions
function updateHeaderFilterState() {
    // Visual feedback can be added here if needed
    console.log('Filters applied:', { gradeFilter, genderFilter, birthYearFilter, dateFromFilter, dateToFilter });
}

// Filter Chips Functions
function updateFilterChips() {
    const container = document.getElementById('filter-chips-container');
    container.innerHTML = '';

    const activeFilters = [];

    if (gradeFilter) {
        activeFilters.push({ type: 'grade', label: `Grade: ${gradeFilter}`, value: gradeFilter });
    }
    if (genderFilter) {
        activeFilters.push({ type: 'gender', label: `Gender: ${genderFilter}`, value: genderFilter });
    }
    if (birthYearFilter) {
        activeFilters.push({ type: 'birthYear', label: `Birth Year: ${birthYearFilter}`, value: birthYearFilter });
    }
    if (dateFromFilter && dateToFilter) {
        activeFilters.push({ type: 'dateRange', label: `Date: ${dateFromFilter} to ${dateToFilter}`, value: `${dateFromFilter}|${dateToFilter}` });
    } else if (dateFromFilter) {
        activeFilters.push({ type: 'dateFrom', label: `From: ${dateFromFilter}`, value: dateFromFilter });
    } else if (dateToFilter) {
        activeFilters.push({ type: 'dateTo', label: `Until: ${dateToFilter}`, value: dateToFilter });
    }

    activeFilters.forEach(filter => {
        const chip = document.createElement('div');
        chip.className = 'filter-chip';
        chip.innerHTML = `
            <span>${filter.label}</span>
            <button class="filter-chip-remove" onclick="removeFilter('${filter.type}')">
                <span class="material-icons">close</span>
            </button>
        `;
        container.appendChild(chip);
    });
}

function removeFilter(filterType) {
    switch (filterType) {
        case 'grade':
            gradeFilter = '';
            if (filterGradeSelect) {
                filterGradeSelect.selectedIndex = 0;
            }
            break;
        case 'gender':
            genderFilter = '';
            if (filterGenderSelect) {
                filterGenderSelect.selectedIndex = 0;
            }
            break;
        case 'birthYear':
            birthYearFilter = '';
            if (filterBirthYearSelect) {
                filterBirthYearSelect.selectedIndex = 0;
            }
            break;
        case 'dateRange':
        case 'dateFrom':
        case 'dateTo':
            dateFromFilter = '';
            dateToFilter = '';
            if (dateFromPicker) {
                dateFromPicker.clear();
            }
            if (dateToPicker) {
                dateToPicker.clear();
            }
            break;
    }
    filterData();
}

function initializeDataTableControls() {
    // Initialize MDC Text Field for search
    const searchTextFieldEl = document.querySelector('.table-controls .mdc-text-field');
    if (searchTextFieldEl && window.mdc && window.mdc.textField) {
        searchTextField = new mdc.textField.MDCTextField(searchTextFieldEl);

        // Add search input listener
        const searchInput = document.getElementById('table-search');
        searchInput.addEventListener('input', (e) => {
            searchQuery = e.target.value;
            filterData();
        });
    }

    // Initialize MDC Select for per-page
    const perPageSelectEl = document.querySelector('.per-page-container .mdc-select');
    if (perPageSelectEl && window.mdc && window.mdc.select) {
        perPageSelect = new mdc.select.MDCSelect(perPageSelectEl);

        // Add change listener
        perPageSelect.listen('MDCSelect:change', () => {
            showTargetedLoading('.data-table-container', 'Loading...');

            simulateLoading(() => {
                perPage = parseInt(perPageSelect.value);
                currentPage = 1;
                renderTable();
                hideTargetedLoading('.data-table-container');
            }, 250);
        });
    }


}

// Event Listeners
document.addEventListener('DOMContentLoaded', () => {
    // Show page preloader initially
    showPagePreloader();

    // Simulate page loading time
    simulateLoading(() => {
        // Initialize MDC Data Table component
        const dataTableEl = document.querySelector('.mdc-data-table');
        if (dataTableEl && window.mdc && window.mdc.dataTable) {
            dataTable = new mdc.dataTable.MDCDataTable(dataTableEl);
        }

        // Initialize MDC Floating Action Button
        const fabEl = document.getElementById('add-student-fab');
        if (fabEl && window.mdc && window.mdc.ripple) {
            const fabRipple = new mdc.ripple.MDCRipple(fabEl);
        }

        // Initialize controls
        initializeDataTableControls();

        // Initialize select-all checkbox
        const selectAllCheckbox = document.getElementById('select-all-checkbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                const rowCheckboxes = document.querySelectorAll('.row-checkbox');
                rowCheckboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
                updateSelectedRowsDisplay();
            });
        }

        // Add sort listeners
        document.querySelectorAll('.mdc-data-table__header-cell--sortable').forEach(th => {
            th.addEventListener('click', (e) => {
                createRipple(e, th, true);
                sortData(th.dataset.column);
            });
        });

        // Add pagination listeners
        document.getElementById('prev-page').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            if (currentPage > 1) {
                showTargetedLoading('.data-table-container', 'Loading...');

                simulateLoading(() => {
                    currentPage--;
                    renderTable();
                    hideTargetedLoading('.data-table-container');
                }, 200);
            }
        });

        document.getElementById('next-page').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            const totalPages = Math.ceil(filteredData.length / perPage);
            if (currentPage < totalPages) {
                showTargetedLoading('.data-table-container', 'Loading...');

                simulateLoading(() => {
                    currentPage++;
                    renderTable();
                    hideTargetedLoading('.data-table-container');
                }, 200);
            }
        });

        // Add event listeners for content header icons
        document.querySelector('.content-header .actions .material-icons[title="Export to Excel"]').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            console.log('Export to Excel clicked');
            // TODO: Implement Excel export functionality
        });

        document.querySelector('.content-header .actions .material-icons[title="Import Data"]').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            console.log('Import Data clicked');
            // TODO: Implement data import functionality
        });

        document.querySelector('.content-header .actions .material-icons[title="Toggle View"]').addEventListener('click', (e) => {
            createRipple(e, e.target, true);
            console.log('Toggle View clicked');
            // TODO: Implement view toggle functionality (list/grid)
        });

        // Add event listener for floating action button
        document.getElementById('add-student-fab').addEventListener('click', () => {
            openAddStudentModal();
        });

        // Add event listener for desktop add student button
        const addStudentBtn = document.getElementById('add-student-btn');
        if (addStudentBtn) {
            // Initialize MDC Button
            if (window.mdc && window.mdc.ripple) {
                new mdc.ripple.MDCRipple(addStudentBtn);
            }

            addStudentBtn.addEventListener('click', () => {
                openAddStudentModal();
            });
        }

        // Initialize modal
        initializeModal();

        // Initial render
        renderTable();

        // Hide page preloader after everything is loaded
        hidePagePreloader();
    }, 800); // Slightly longer delay to show the preloader
});

// Modal Functions
function initializeModal() {
    modalOverlay = document.getElementById('modal-overlay');
    modal = document.getElementById('modal');
    modalForm = document.getElementById('modal-form');
    modalTitle = document.getElementById('modal-title');
    modalSubmitBtn = document.getElementById('modal-submit');
    modalCancelBtn = document.getElementById('modal-cancel');

    // Initialize MDC buttons
    if (window.mdc && window.mdc.ripple) {
        new mdc.ripple.MDCRipple(modalSubmitBtn);
        new mdc.ripple.MDCRipple(modalCancelBtn);
    }

    // Add event listeners
    document.getElementById('modal-close').addEventListener('click', closeModal);
    modalCancelBtn.addEventListener('click', closeModal);
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            closeModal();
        }
    });

    // Handle form submission
    modalForm.addEventListener('submit', handleModalSubmit);

    // Handle escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && modalOverlay.classList.contains('active')) {
            closeModal();
        }
    });
}

function openModal(config) {
    const {
        title = 'Modal',
        fields = [],
        submitText = 'Save',
        onSubmit = null,
        data = {}
    } = config;

    // Set modal title
    modalTitle.textContent = title;
    modalSubmitBtn.querySelector('.mdc-button__label').textContent = submitText;

    // Clear and populate form
    modalForm.innerHTML = '';

    fields.forEach(field => {
        const fieldElement = createFormField(field, data[field.name] || '');
        modalForm.appendChild(fieldElement);
    });

    // Initialize MDC components for new fields
    initializeModalFormComponents();

    // Store submit handler
    modalForm.onSubmitHandler = onSubmit;

    // Show modal
    modalOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';

    // Focus first input
    setTimeout(() => {
        const firstInput = modalForm.querySelector('input, select, textarea');
        if (firstInput) {
            firstInput.focus();
        }
    }, 300);
}

function closeModal() {
    modalOverlay.classList.remove('active');
    document.body.style.overflow = '';

    // Clear form after animation
    setTimeout(() => {
        modalForm.innerHTML = '';
        modalForm.onSubmitHandler = null;
    }, 300);
}

function createFormField(field, value = '') {
    const {
        name,
        label,
        type = 'text',
        required = false,
        options = [],
        placeholder = '',
        rows = 3
    } = field;

    const fieldDiv = document.createElement('div');
    fieldDiv.className = 'form-field';

    if (type === 'select') {
        fieldDiv.innerHTML = `
            <label>${label}${required ? ' *' : ''}</label>
            <div class="mdc-select mdc-select--outlined">
                <div class="mdc-select__anchor" role="button" aria-haspopup="listbox">
                    <span class="mdc-select__selected-text-container">
                        <span class="mdc-select__selected-text"></span>
                    </span>
                    <span class="mdc-select__dropdown-icon">
                        <svg class="mdc-select__dropdown-icon-graphic" viewBox="7 10 10 5">
                            <polygon class="mdc-select__dropdown-icon-inactive" stroke="none" fill-rule="evenodd" points="7 10 12 15 17 10"></polygon>
                            <polygon class="mdc-select__dropdown-icon-active" stroke="none" fill-rule="evenodd" points="7 15 12 10 17 15"></polygon>
                        </svg>
                    </span>
                    <div class="mdc-notched-outline">
                        <div class="mdc-notched-outline__leading"></div>
                        <div class="mdc-notched-outline__trailing"></div>
                    </div>
                </div>
                <div class="mdc-select__menu mdc-menu mdc-menu-surface mdc-menu-surface--fullwidth">
                    <ul class="mdc-deprecated-list" role="listbox">
                        ${options.map(option => `
                            <li class="mdc-deprecated-list-item ${option.value === value ? 'mdc-deprecated-list-item--selected' : ''}"
                                data-value="${option.value}" role="option" ${option.value === value ? 'aria-selected="true"' : ''}>
                                <span class="mdc-deprecated-list-item__ripple"></span>
                                <span class="mdc-deprecated-list-item__text">${option.label}</span>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            </div>
        `;
    } else if (type === 'textarea') {
        fieldDiv.innerHTML = `
            <label>${label}${required ? ' *' : ''}</label>
            <div class="mdc-text-field mdc-text-field--outlined mdc-text-field--textarea">
                <span class="mdc-notched-outline">
                    <span class="mdc-notched-outline__leading"></span>
                    <span class="mdc-notched-outline__notch">
                        <span class="mdc-floating-label">${label}</span>
                    </span>
                    <span class="mdc-notched-outline__trailing"></span>
                </span>
                <span class="mdc-text-field__resizer">
                    <textarea class="mdc-text-field__input" name="${name}" rows="${rows}"
                              ${required ? 'required' : ''} placeholder="${placeholder}">${value}</textarea>
                </span>
            </div>
        `;
    } else {
        fieldDiv.innerHTML = `
            <label>${label}${required ? ' *' : ''}</label>
            <div class="mdc-text-field mdc-text-field--outlined">
                <input type="${type}" class="mdc-text-field__input" name="${name}"
                       value="${value}" ${required ? 'required' : ''} placeholder="${placeholder}">
                <div class="mdc-notched-outline">
                    <div class="mdc-notched-outline__leading"></div>
                    <div class="mdc-notched-outline__notch">
                        <label class="mdc-floating-label">${label}</label>
                    </div>
                    <div class="mdc-notched-outline__trailing"></div>
                </div>
            </div>
        `;
    }

    return fieldDiv;
}

function initializeModalFormComponents() {
    // Initialize MDC text fields
    modalForm.querySelectorAll('.mdc-text-field').forEach(textField => {
        if (window.mdc && window.mdc.textField) {
            new mdc.textField.MDCTextField(textField);
        }
    });

    // Initialize MDC select fields
    modalForm.querySelectorAll('.mdc-select').forEach(select => {
        if (window.mdc && window.mdc.select) {
            new mdc.select.MDCSelect(select);
        }
    });
}

function handleModalSubmit(e) {
    e.preventDefault();

    if (modalForm.onSubmitHandler) {
        const formData = new FormData(modalForm);
        const data = Object.fromEntries(formData.entries());
        modalForm.onSubmitHandler(data);
    }

    closeModal();
}

function openAddStudentModal() {
    const studentFields = [
        {
            name: 'firstName',
            label: 'First Name',
            type: 'text',
            required: true,
            placeholder: 'Enter first name'
        },
        {
            name: 'lastName',
            label: 'Last Name',
            type: 'text',
            required: true,
            placeholder: 'Enter last name'
        },
        {
            name: 'email',
            label: 'Email',
            type: 'email',
            required: true,
            placeholder: 'Enter email address'
        },
        {
            name: 'phone',
            label: 'Phone Number',
            type: 'tel',
            placeholder: 'Enter phone number'
        },
        {
            name: 'grade',
            label: 'Grade',
            type: 'select',
            required: true,
            options: [
                { value: '', label: 'Select Grade' },
                { value: 'Grade 9', label: 'Grade 9' },
                { value: 'Grade 10', label: 'Grade 10' },
                { value: 'Grade 11', label: 'Grade 11' },
                { value: 'Grade 12', label: 'Grade 12' }
            ]
        },
        {
            name: 'birthDate',
            label: 'Birth Date',
            type: 'date',
            required: true
        },
        {
            name: 'gender',
            label: 'Gender',
            type: 'select',
            required: true,
            options: [
                { value: '', label: 'Select Gender' },
                { value: 'Male', label: 'Male' },
                { value: 'Female', label: 'Female' },
                { value: 'Other', label: 'Other' }
            ]
        },
        {
            name: 'address',
            label: 'Address',
            type: 'textarea',
            placeholder: 'Enter full address',
            rows: 3
        }
    ];

    openModal({
        title: 'Add New Student',
        fields: studentFields,
        submitText: 'Add Student',
        onSubmit: handleAddStudent
    });
}

function handleAddStudent(data) {
    console.log('Adding new student:', data);

    // Show loading
    showLoadingOverlay('Adding student...');

    // Simulate API call
    setTimeout(() => {
        // Generate new student ID
        const newId = `ST${new Date().getFullYear()}${String(studentsData.length + 1).padStart(3, '0')}`;

        // Create new student object
        const newStudent = {
            id: newId,
            name: `${data.firstName} ${data.lastName}`,
            level: data.grade,
            birth: new Date(data.birthDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            }),
            birthYear: new Date(data.birthDate).getFullYear(),
            gender: data.gender,
            photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face',
            date: new Date().toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            dateAdded: new Date().toISOString().split('T')[0],
            email: data.email,
            phone: data.phone,
            address: data.address
        };

        // Add to students data
        studentsData.unshift(newStudent);

        // Refresh the display
        filterData();

        hideLoadingOverlay();

        // Show success message (you can implement a toast/snackbar here)
        console.log('Student added successfully!');
    }, 1000);
}

// Ripple Effect Function
function createRipple(event, element, isDark = false) {
    const circle = document.createElement('span');
    const diameter = Math.max(element.clientWidth, element.clientHeight);
    const radius = diameter / 2;
    
    const rect = element.getBoundingClientRect();
    circle.style.width = circle.style.height = `${diameter}px`;
    circle.style.left = `${event.clientX - rect.left - radius}px`;
    circle.style.top = `${event.clientY - rect.top - radius}px`;
    circle.classList.add('ripple');
    
    if (isDark) {
        circle.classList.add('ripple-dark');
    }
    
    const ripple = element.getElementsByClassName('ripple')[0];
    if (ripple) {
        ripple.remove();
    }
    
    element.appendChild(circle);
    
    // Remove ripple after animation
    setTimeout(() => {
        circle.remove();
    }, 600);
}

// Sidebar toggle (works for both mobile and desktop)
const menuBtn = document.getElementById('menu-btn');
const sidebar = document.getElementById('sidebar');
const mainContent = document.querySelector('.main-content');

menuBtn.addEventListener('click', (e) => {
    createRipple(e, menuBtn);

    if (window.innerWidth <= 768) {
        // Mobile behavior: toggle open class
        sidebar.classList.toggle('open');
    } else {
        // Desktop behavior: toggle collapsed class
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
    }
});

// Close sidebar when clicking outside on mobile
document.addEventListener('click', (e) => {
    if (window.innerWidth <= 768) {
        if (!sidebar.contains(e.target) && !menuBtn.contains(e.target)) {
            sidebar.classList.remove('open');
        }
    }
});

// Grades submenu toggle
const gradesMenu = document.getElementById('grades-menu');
const gradesSubmenu = document.getElementById('grades-submenu');

gradesMenu.addEventListener('click', (e) => {
    createRipple(e, gradesMenu, true);
    gradesMenu.classList.toggle('expanded');
    gradesSubmenu.classList.toggle('expanded');
});

// Sidebar item interactions
document.querySelectorAll('.sidebar-item:not(#grades-menu)').forEach(item => {
    item.addEventListener('click', (e) => {
        createRipple(e, item, true);
        // Remove active class from all items
        document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
        document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked item
        item.classList.add('active');
    });
});

// Submenu item interactions
document.querySelectorAll('.submenu-item').forEach(item => {
    item.addEventListener('click', (e) => {
        e.stopPropagation();
        createRipple(e, item, true);
        // Remove active class from all items
        document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
        document.querySelectorAll('.submenu-item').forEach(i => i.classList.remove('active'));
        // Add active class to clicked submenu item
        item.classList.add('active');
    });
});

// Edit button interactions
document.querySelectorAll('.edit-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
        e.stopPropagation();
        createRipple(e, btn, true);
        const studentName = btn.closest('.student-item').querySelector('.student-name').textContent;
        console.log('Edit student:', studentName);
        // Here you would typically open an edit modal or navigate to edit page
    });
});

// Bottom Sheet Elements
const bottomSheetOverlay = document.getElementById('bottom-sheet-overlay');
const bottomSheet = document.getElementById('bottom-sheet');
const bottomSheetClose = document.getElementById('bottom-sheet-close');
const previewPhoto = document.getElementById('preview-photo');
const previewName = document.getElementById('preview-name');
const previewDetails = document.getElementById('preview-details');

// Bottom Sheet Functions
function showBottomSheet(studentData) {
    // Populate student preview
    previewPhoto.style.backgroundImage = studentData.photo;
    previewName.textContent = studentData.name;
    previewDetails.innerHTML = `<span class="student-level">${studentData.level}</span>${studentData.details}`;

    // Show bottom sheet
    bottomSheetOverlay.classList.add('active');
    bottomSheet.classList.add('active');

    // Store current student data for actions
    bottomSheet.dataset.studentId = studentData.id;
    bottomSheet.dataset.studentName = studentData.name;
}

function hideBottomSheet() {
    bottomSheetOverlay.classList.remove('active');
    bottomSheet.classList.remove('active');
}

// Bottom Sheet Event Listeners
bottomSheetClose.addEventListener('click', (e) => {
    createRipple(e, bottomSheetClose, true);
    hideBottomSheet();
});

bottomSheetOverlay.addEventListener('click', (e) => {
    if (e.target === bottomSheetOverlay) {
        hideBottomSheet();
    }
});

// Student item interactions
document.querySelectorAll('.student-item').forEach(item => {
    item.addEventListener('click', (e) => {
        // Don't trigger if edit button was clicked
        if (e.target.closest('.edit-btn')) return;

        createRipple(e, item, true);

        // Extract student data
        const studentData = {
            id: item.dataset.studentId || item.querySelector('.student-name').textContent.replace(/\s+/g, '').toLowerCase(),
            name: item.querySelector('.student-name').textContent,
            level: item.querySelector('.student-level').textContent,
            details: item.querySelector('.student-details').textContent.replace(item.querySelector('.student-level').textContent, '').trim(),
            photo: item.querySelector('.student-photo').style.backgroundImage
        };

        showBottomSheet(studentData);
    });
});

// Add ripple effects to app bar action buttons
document.querySelectorAll('.app-bar .actions .material-icons').forEach(icon => {
    icon.addEventListener('click', (e) => {
        createRipple(e, icon);
    });
});

// Add ripple effects to content header icons
document.querySelectorAll('.content-header .material-icons').forEach(icon => {
    icon.addEventListener('click', (e) => {
        createRipple(e, icon, true);
    });
});

// Bottom Sheet Action Handlers
document.getElementById('action-details').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('View details for:', studentName);
    hideBottomSheet();
    // Here you would navigate to student details page
});

document.getElementById('action-edit').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('Edit student:', studentName);
    hideBottomSheet();
    // Here you would open edit modal or navigate to edit page
});

document.getElementById('action-payment').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    console.log('Add payment record for:', studentName);
    hideBottomSheet();
    // Here you would open payment modal or navigate to payment page
});

document.getElementById('action-delete').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    const studentName = bottomSheet.dataset.studentName;
    const studentId = bottomSheet.dataset.studentId;

    // Show confirmation dialog
    if (confirm(`Are you sure you want to delete ${studentName}? This action cannot be undone.`)) {
        console.log('Delete student:', studentName, 'ID:', studentId);
        hideBottomSheet();
        // Here you would call delete API and remove from DOM
        // Example: removeStudentFromList(studentId);
    }
});

// Add ripple effects to bottom sheet actions
document.querySelectorAll('.bottom-sheet-action').forEach(action => {
    action.addEventListener('click', (e) => {
        if (!e.currentTarget.id) { // Only add ripple if not handled by specific action
            createRipple(e, action, true);
        }
    });
});

// Keyboard support for bottom sheet
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && bottomSheetOverlay.classList.contains('active')) {
        hideBottomSheet();
    }
});

// Responsive sidebar handling
window.addEventListener('resize', () => {
    if (window.innerWidth > 768) {
        // Desktop: remove mobile classes
        sidebar.classList.remove('open');
    } else {
        // Mobile: remove desktop classes and reset to mobile behavior
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('expanded');
    }
});

// Offcanvas Component
const offcanvasOverlay = document.getElementById('offcanvas-overlay');
const offcanvas = document.getElementById('offcanvas');
const offcanvasClose = document.getElementById('offcanvas-close');
const moreMenuBtn = document.getElementById('more-menu-btn');

// Offcanvas Functions
function showOffcanvas() {
    offcanvasOverlay.classList.add('active');
    offcanvas.classList.add('active');
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
}

function hideOffcanvas() {
    offcanvasOverlay.classList.remove('active');
    offcanvas.classList.remove('active');
    document.body.style.overflow = ''; // Restore scrolling
}

// Offcanvas Event Listeners
moreMenuBtn.addEventListener('click', (e) => {
    createRipple(e, moreMenuBtn);
    showOffcanvas();
});

offcanvasClose.addEventListener('click', (e) => {
    createRipple(e, offcanvasClose);
    hideOffcanvas();
});

offcanvasOverlay.addEventListener('click', (e) => {
    if (e.target === offcanvasOverlay) {
        hideOffcanvas();
    }
});

// Offcanvas Menu Item Event Listeners
document.getElementById('menu-profile').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    console.log('Edit Profile clicked');
    hideOffcanvas();
    // TODO: Implement profile editing functionality
});

document.getElementById('menu-settings').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    console.log('Settings clicked');
    hideOffcanvas();
    // TODO: Implement settings functionality
});

document.getElementById('menu-notifications').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    console.log('Notifications clicked');
    hideOffcanvas();
    // TODO: Implement notifications functionality
});

document.getElementById('menu-help').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);
    console.log('Help & Support clicked');
    hideOffcanvas();
    // TODO: Implement help functionality
});

document.getElementById('menu-logout').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget, true);

    // Show confirmation dialog
    if (confirm('Are you sure you want to logout?')) {
        console.log('User logged out');
        hideOffcanvas();
        // TODO: Implement logout functionality
        // Example: window.location.href = '/login';
    }
});

// Keyboard support for offcanvas
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && offcanvasOverlay.classList.contains('active')) {
        hideOffcanvas();
    }
});

// Add ripple effects to offcanvas menu items
document.querySelectorAll('.offcanvas-menu-item').forEach(item => {
    item.addEventListener('click', (e) => {
        if (!e.currentTarget.id) { // Only add ripple if not handled by specific action
            createRipple(e, item, true);
        }
    });
});

// Filter Offcanvas Component
const filterOffcanvasOverlay = document.getElementById('filter-offcanvas-overlay');
const filterOffcanvas = document.getElementById('filter-offcanvas');
const filterOffcanvasClose = document.getElementById('filter-offcanvas-close');
const filterBtn = document.getElementById('filter-btn');

// Filter Offcanvas Functions
function showFilterOffcanvas() {
    filterOffcanvasOverlay.classList.add('active');
    filterOffcanvas.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function hideFilterOffcanvas() {
    filterOffcanvasOverlay.classList.remove('active');
    filterOffcanvas.classList.remove('active');
    document.body.style.overflow = '';
}

// Initialize Filter Components
function initializeFilterComponents() {
    // Initialize Grade Filter Select
    const gradeFilterEl = document.getElementById('grade-filter-select');
    if (gradeFilterEl && window.mdc && window.mdc.select) {
        filterGradeSelect = new mdc.select.MDCSelect(gradeFilterEl);
        filterGradeSelect.listen('MDCSelect:change', () => {
            gradeFilter = filterGradeSelect.value;
            filterData();
        });
    }

    // Initialize Gender Filter Select
    const genderFilterEl = document.getElementById('gender-filter-select');
    if (genderFilterEl && window.mdc && window.mdc.select) {
        filterGenderSelect = new mdc.select.MDCSelect(genderFilterEl);
        filterGenderSelect.listen('MDCSelect:change', () => {
            genderFilter = filterGenderSelect.value;
            filterData();
        });
    }

    // Initialize Birth Year Filter Select
    const birthYearFilterEl = document.getElementById('birth-year-filter-select');
    if (birthYearFilterEl && window.mdc && window.mdc.select) {
        filterBirthYearSelect = new mdc.select.MDCSelect(birthYearFilterEl);
        filterBirthYearSelect.listen('MDCSelect:change', () => {
            birthYearFilter = filterBirthYearSelect.value;
            filterData();
        });
    }

    // Initialize Date Pickers
    const dateFromInput = document.getElementById('date-from-filter');
    const dateToInput = document.getElementById('date-to-filter');

    if (dateFromInput && window.flatpickr) {
        dateFromPicker = flatpickr(dateFromInput, {
            dateFormat: 'Y-m-d',
            placeholder: 'Select from date',
            onChange: function(selectedDates, dateStr) {
                dateFromFilter = dateStr;
                filterData();
            }
        });
    }

    if (dateToInput && window.flatpickr) {
        dateToPicker = flatpickr(dateToInput, {
            dateFormat: 'Y-m-d',
            placeholder: 'Select to date',
            onChange: function(selectedDates, dateStr) {
                dateToFilter = dateStr;
                filterData();
            }
        });
    }

    // Initialize Date Input Text Fields
    const dateFromTextField = document.querySelector('#date-from-filter').closest('.mdc-text-field');
    const dateToTextField = document.querySelector('#date-to-filter').closest('.mdc-text-field');

    if (dateFromTextField && window.mdc && window.mdc.textField) {
        new mdc.textField.MDCTextField(dateFromTextField);
    }

    if (dateToTextField && window.mdc && window.mdc.textField) {
        new mdc.textField.MDCTextField(dateToTextField);
    }

    // Initialize Clear Filters Button
    const clearFiltersBtn = document.getElementById('clear-filters-btn');
    if (clearFiltersBtn && window.mdc && window.mdc.ripple) {
        new mdc.ripple.MDCRipple(clearFiltersBtn);
    }
}

// Clear all filters function
function clearAllFilters() {
    // Reset filter variables
    gradeFilter = '';
    genderFilter = '';
    birthYearFilter = '';
    dateFromFilter = '';
    dateToFilter = '';

    // Reset filter components
    if (filterGradeSelect) {
        filterGradeSelect.selectedIndex = 0;
    }
    if (filterGenderSelect) {
        filterGenderSelect.selectedIndex = 0;
    }
    if (filterBirthYearSelect) {
        filterBirthYearSelect.selectedIndex = 0;
    }
    if (dateFromPicker) {
        dateFromPicker.clear();
    }
    if (dateToPicker) {
        dateToPicker.clear();
    }

    // Apply filters (which will show all data)
    filterData();
}

// Filter Offcanvas Event Listeners
filterBtn.addEventListener('click', (e) => {
    createRipple(e, filterBtn, true);
    showFilterOffcanvas();
});

filterOffcanvasClose.addEventListener('click', (e) => {
    createRipple(e, filterOffcanvasClose);
    hideFilterOffcanvas();
});

filterOffcanvasOverlay.addEventListener('click', (e) => {
    if (e.target === filterOffcanvasOverlay) {
        hideFilterOffcanvas();
    }
});

document.getElementById('clear-filters-btn').addEventListener('click', (e) => {
    createRipple(e, e.currentTarget);
    clearAllFilters();
});

// Initialize filter components when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Add a small delay to ensure all MDC components are initialized
    setTimeout(() => {
        initializeFilterComponents();
    }, 1000);
});

// Keyboard support for filter offcanvas
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && filterOffcanvasOverlay.classList.contains('active')) {
        hideFilterOffcanvas();
    }
});
